import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { invoiceGeneratorAPI } from "../../../../services/api";

const MizuTemplateSelection = ({ onSelect, onBack, variants }) => {
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      setError("");

      // Load templates with previews from API
      const response = await invoiceGeneratorAPI.getTemplatesWithPreviews();
      const templatesData = response.data?.templates || [];

      if (templatesData.length === 0) {
        setError("No templates available from the server.");
        return;
      }

      // Ensure preview URLs are set correctly
      const templatesWithPreviews = templatesData.map(template => ({
        ...template,
        template_type_display: template.template_type_display || template.name,
        preview_url: template.preview_url || invoiceGeneratorAPI.getTemplatePreviewUrl(template.id)
      }));

      setTemplates(templatesWithPreviews);
      console.log('Loaded templates:', templatesWithPreviews);
    } catch (err) {
      console.error('Error loading templates:', err);
      setError("Failed to load templates. Please check your connection and try again.");
    } finally {
      setLoading(false);
    }
  };

  const getTemplateIcon = (templateType) => {
    switch (templateType) {
      case 'minimalist':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
          </svg>
        );
      case 'corporate':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
        );
      case 'contemporary':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
          </svg>
        );
      case 'clean_business':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
          </svg>
        );
      case 'elegant_classic':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-amber-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        );
    }
  };

  const getTemplateColor = (templateType) => {
    switch (templateType) {
      case 'minimalist':
        return {
          bg: 'bg-blue-50',
          border: 'border-blue-200',
          hover: 'hover:border-blue-400',
          text: 'text-blue-700'
        };
      case 'corporate':
        return {
          bg: 'bg-gray-50',
          border: 'border-gray-200',
          hover: 'hover:border-gray-400',
          text: 'text-gray-700'
        };
      case 'contemporary':
        return {
          bg: 'bg-purple-50',
          border: 'border-purple-200',
          hover: 'hover:border-purple-400',
          text: 'text-purple-700'
        };
      case 'clean_business':
        return {
          bg: 'bg-green-50',
          border: 'border-green-200',
          hover: 'hover:border-green-400',
          text: 'text-green-700'
        };
      case 'elegant_classic':
        return {
          bg: 'bg-amber-50',
          border: 'border-amber-200',
          hover: 'hover:border-amber-400',
          text: 'text-amber-700'
        };
      default:
        return {
          bg: 'bg-gray-50',
          border: 'border-gray-200',
          hover: 'hover:border-gray-400',
          text: 'text-gray-700'
        };
    }
  };

  return (
    <motion.div
      className="flex flex-col gap-6"
      variants={variants}
    >
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={onBack}
          className="flex items-center text-blue-600 hover:text-blue-800 transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          Back
        </button>
        <h2 className="text-xl font-medium text-gray-700">
          Choose a MizuFlow Template
        </h2>
        <div className="w-20"></div> {/* Spacer for alignment */}
      </div>

      <p className="text-gray-600 text-center max-w-2xl mx-auto">
        Select from our professionally designed invoice templates. You can customize company information and styling after selection.
      </p>

      {loading ? (
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : error ? (
        <div className="text-center py-8 bg-red-50 rounded-lg border border-red-200">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-red-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className="text-red-600 mb-2">{error}</p>
          <button
            className="text-red-600 hover:text-red-800 font-medium"
            onClick={loadTemplates}
          >
            Try again
          </button>
        </div>
      ) : templates.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
          </svg>
          <p className="text-gray-600">No templates available</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.isArray(templates) && templates.map((template) => {
            const colors = getTemplateColor(template.template_type);
            return (
              <motion.div
                key={template.id}
                className={`${colors.bg} border-2 ${colors.border} ${colors.hover} rounded-xl shadow-sm hover:shadow-md transition-all p-6 cursor-pointer`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => onSelect(template)}
              >
                <div className="flex items-center mb-4">
                  <div className="mr-4">
                    {getTemplateIcon(template.template_type)}
                  </div>
                  <div>
                    <h3 className={`text-lg font-semibold ${colors.text}`}>
                      {template.name}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {template.template_type_display}
                    </p>
                  </div>
                </div>

                <p className="text-gray-600 text-sm mb-4">
                  {template.description}
                </p>

                {/* Template preview */}
                <div className="bg-white border border-gray-200 rounded-lg p-4 mb-4 relative overflow-hidden">
                  {template.preview_url ? (
                    <iframe
                      src={template.preview_url}
                      className="w-full h-32 border-0 pointer-events-none transform scale-50 origin-top-left"
                      style={{ width: '200%', height: '64px' }}
                      title={`Preview of ${template.name}`}
                    />
                  ) : (
                    <div className="space-y-2">
                      <div className={`h-2 ${colors.bg} rounded w-3/4`}></div>
                      <div className={`h-2 ${colors.bg} rounded w-1/2`}></div>
                      <div className="h-1 bg-gray-100 rounded w-full mt-3"></div>
                      <div className="h-1 bg-gray-100 rounded w-full"></div>
                      <div className="h-1 bg-gray-100 rounded w-2/3"></div>
                    </div>
                  )}
                  <div className="absolute inset-0 bg-gradient-to-t from-white/80 to-transparent pointer-events-none"></div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex gap-2">
                    {template.preview_url && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          window.open(template.preview_url, '_blank');
                        }}
                        className={`text-xs px-3 py-1 rounded-full border ${colors.border} ${colors.text} hover:bg-white transition-colors`}
                      >
                        Preview
                      </button>
                    )}
                    <span className={`text-sm font-medium ${colors.text}`}>
                      Select Template
                    </span>
                  </div>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </motion.div>
            );
          })}
        </div>
      )}
    </motion.div>
  );
};

export default MizuTemplateSelection;
