from rest_framework import status, viewsets
from rest_framework.response import Response
from rest_framework.decorators import action
from django.http import JsonResponse, HttpResponse
from django.shortcuts import render, get_object_or_404
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import api_view, permission_classes
from .models import (
    CompanyProfile,
    CustomTemplate,
    InvoiceTemplate,
)
from .serializers import (
    CompanyProfileSerializer,
    CustomTemplateSerializer,
    InvoiceTemplateSerializer,
    TemplatePreviewSerializer,
    CustomTemplateCreateSerializer,
)
from .utils import (
    validate_template_data,
    render_invoice_template,
    get_template_preview_data,
    generate_template_preview_html,
)


@api_view(["POST"])
def extract_template_information(request):
    """
    Endpoint to extract information from an uploaded invoice PDF file.
    Returns dummy data for now.
    """
    if "file" not in request.FILES:
        return JsonResponse({"error": "No PDF file provided"}, status=400)

    pdf_file = request.FILES["file"]

    # Here we would process the PDF, but for now we return dummy data
    invoice_data = {
        "company_name": "Example Company Ltd",
        "address": "123 Business Street, City, Country",
        "contact_email": "<EMAIL>",
        "payment_terms": "Net 30 days",
        "bank_info": "Bank: Example Bank, Account: ********, Sort Code: 01-02-03",
        "company_logo": "base64_encoded_image_data_would_be_here",
        "Template Name": "",
    }

    return JsonResponse(invoice_data)


class InvoiceTemplateViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for default invoice templates (read-only)"""

    queryset = InvoiceTemplate.objects.filter(is_active=True)
    serializer_class = InvoiceTemplateSerializer
    permission_classes = [IsAuthenticated]


@api_view(["GET", "POST", "PUT"])
@permission_classes([IsAuthenticated])
def company_profile_view(request):
    """
    Handle company profile operations
    GET: Retrieve user's company profile
    POST: Create company profile
    PUT: Update company profile
    """
    try:
        profile = CompanyProfile.objects.get(user=request.user)
    except CompanyProfile.DoesNotExist:
        profile = None

    if request.method == "GET":
        if profile:
            serializer = CompanyProfileSerializer(profile)
            return Response(serializer.data)
        else:
            return Response({}, status=status.HTTP_200_OK)

    elif request.method == "POST":
        if profile:
            return Response(
                {"error": "Company profile already exists. Use PUT to update."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = CompanyProfileSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(user=request.user)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    elif request.method == "PUT":
        if not profile:
            # Create if doesn't exist
            serializer = CompanyProfileSerializer(data=request.data)
            if serializer.is_valid():
                serializer.save(user=request.user)
                return Response(serializer.data, status=status.HTTP_201_CREATED)
        else:
            # Update existing
            serializer = CompanyProfileSerializer(
                profile, data=request.data, partial=True
            )
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CustomTemplateViewSet(viewsets.ModelViewSet):
    """ViewSet for custom user templates"""

    serializer_class = CustomTemplateSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return CustomTemplate.objects.filter(user=self.request.user, is_active=True)

    def get_serializer_class(self):
        if self.action == "create":
            return CustomTemplateCreateSerializer
        return CustomTemplateSerializer

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    @action(detail=True, methods=["post"])
    def mark_used(self, request, pk=None):
        """Mark template as used"""
        template = self.get_object()
        template.mark_as_used()
        return Response({"status": "Template marked as used"})


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def generate_template_preview(request):
    """
    Generate a preview of a template with company information
    """
    serializer = TemplatePreviewSerializer(data=request.data)
    if serializer.is_valid():
        template_id = serializer.validated_data["template_id"]
        company_info = serializer.validated_data["company_info"]
        custom_layout = serializer.validated_data.get("custom_layout_config", {})
        custom_style = serializer.validated_data.get("custom_style_config", {})

        try:
            template = InvoiceTemplate.objects.get(id=template_id, is_active=True)
        except InvoiceTemplate.DoesNotExist:
            return Response(
                {"error": "Template not found"}, status=status.HTTP_404_NOT_FOUND
            )

        # Generate actual HTML preview
        try:
            html_content = generate_template_preview_html(template_id, company_info)
            preview_data = {
                "template_id": str(template_id),
                "template_name": template.name,
                "template_type": template.template_type,
                "html_content": html_content,
                "preview_url": f"/api/v1/invoice-generator/preview/{template_id}/",
                "company_info": company_info,
                "generated_at": "2024-01-01T00:00:00Z",
            }
            return Response(preview_data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                {"error": f"Failed to generate preview: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(["GET"])
def template_preview_html(request, template_id):
    """
    Return HTML preview of a template for direct viewing
    """
    try:
        template = InvoiceTemplate.objects.get(id=template_id, is_active=True)
    except InvoiceTemplate.DoesNotExist:
        return HttpResponse("Template not found", status=404)

    try:
        # Get company info from query params or use defaults
        company_info = {}
        if request.user.is_authenticated:
            try:
                profile = CompanyProfile.objects.get(user=request.user)
                company_info = {
                    'company_name': profile.company_name,
                    'address_line_1': profile.address_line_1,
                    'address_line_2': profile.address_line_2,
                    'city': profile.city,
                    'state_province': profile.state_province,
                    'postal_code': profile.postal_code,
                    'country': profile.country,
                    'phone': profile.phone,
                    'email': profile.email,
                    'website': profile.website,
                    'tax_id': profile.tax_id,
                    'business_registration': profile.business_registration,
                    'bank_name': profile.bank_name,
                    'account_number': profile.account_number,
                    'routing_number': profile.routing_number,
                    'swift_code': profile.swift_code,
                    'default_payment_terms': profile.default_payment_terms,
                    'logo': profile.logo.url if profile.logo else None,
                }
            except CompanyProfile.DoesNotExist:
                pass

        html_content = generate_template_preview_html(template_id, company_info)
        return HttpResponse(html_content, content_type='text/html')
    except Exception as e:
        return HttpResponse(f"Error generating preview: {str(e)}", status=500)


@api_view(["GET"])
def template_list_with_previews(request):
    """
    Get all templates with preview thumbnails
    """
    templates = InvoiceTemplate.objects.filter(is_active=True)
    template_data = []

    for template in templates:
        try:
            # Generate preview data
            preview_info = get_template_preview_data(template.template_type)
            template_info = {
                'id': str(template.id),
                'name': template.name,
                'template_type': template.template_type,
                'description': template.description,
                'preview_url': f"/api/v1/invoice-generator/preview/{template.id}/",
                'preview_available': preview_info['success'] if preview_info else False,
            }
            template_data.append(template_info)
        except Exception as e:
            # Include template even if preview fails
            template_info = {
                'id': str(template.id),
                'name': template.name,
                'template_type': template.template_type,
                'description': template.description,
                'preview_url': f"/api/v1/invoice-generator/preview/{template.id}/",
                'preview_available': False,
                'error': str(e)
            }
            template_data.append(template_info)

    return Response({
        'templates': template_data,
        'count': len(template_data)
    })